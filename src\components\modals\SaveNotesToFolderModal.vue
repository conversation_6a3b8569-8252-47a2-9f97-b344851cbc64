<template>
  <div class="modal-content">
      <h3>Save Notes to Folder</h3>
      <div class="save-info">
        <p>Select destination folder for the selected note(s):</p>
        <div class="selected-items-summary">
          <template v-if="notes.length > 1">
            <p>{{ notes.length }} notes</p>
          </template>
          <template v-else>
            <p>{{ notes.length }} note</p>
          </template>
        </div>
      </div>
      
      <div class="folder-list-container">
        <div class="folder-list">
          <!-- Root folder option -->
          <div 
            class="folder-item"
            :class="{ 
              'selected': selectedFolderId === null,
              'disabled': isRootDisabled 
            }"
            @click="!isRootDisabled && selectFolder(null)"
          >
            <img src="/icons/folder-icon.svg" alt="Root Folder" class="folder-icon" />
            <span :class="{ 'disabled-text': isRootDisabled }">All Folders (Root)</span>
            <span v-if="isRootDisabled" class="disabled-reason">(Current folder)</span>
          </div>
          
          <!-- Available folders -->
          <div v-for="folder in folders" :key="folder.id" 
               class="folder-item"
               :class="{ 
                 'selected': selectedFolderId === folder.id,
                 'disabled': isDisabled(folder)
               }"
               @click="!isDisabled(folder) && selectFolder(folder.id)"
          >
            <div class="folder-indent" :style="{ paddingLeft: `${(folder.level || 0) * 16}px` }">
              <FolderIcon :color="folder.color || undefined" :size="18" :isOpen="false" class="folder-icon" />
              <span :class="{ 'disabled-text': isDisabled(folder) }">{{ folder.name }}</span>
              <span v-if="isDisabled(folder)" class="disabled-reason">(Current folder)</span>
            </div>
          </div>
          
          <div v-if="folders.length === 0" class="no-folders">
            <p>No available destination folders</p>
          </div>
        </div>
      </div>

      <div class="selected-destination" v-if="selectedFolderId !== undefined && !errorMessage">
        <div class="destination-info">
          <img src="/icons/folder-icon.svg" alt="Destination" class="folder-icon" />
          <span>Save to: <strong>{{ getSelectedFolderName }}</strong></span>
        </div>
      </div>
      
      <div class="modal-buttons">
        <button @click="cancel" class="btn btn-secondary" :disabled="isLoading">Cancel</button>
        <div class="button-container">
          <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
          <button 
            @click="confirm" 
            class="btn btn-primary"
            :disabled="!canConfirm || isLoading"
          >
            <span v-if="isLoading" class="loading-spinner"></span>
            <span>{{ isLoading ? 'Saving...' : 'Save' }}</span>
          </button>
        </div>
      </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import type { Note, Folder } from '../../types/electron-api';
import FolderIcon from '../icons/FolderIcon.vue';

interface FolderWithLevel extends Folder {
  level?: number;
}

export default defineComponent({
  name: 'SaveNotesToFolderModal',
  components: {
    FolderIcon
  },
  props: {
    notes: {
      type: Array as PropType<Note[]>,
      required: true
    },
    folders: {
      type: Array as PropType<FolderWithLevel[]>,
      required: true
    },
    currentFolderId: {
      type: Number as PropType<number | null>,
      default: null
    }
  },
  emits: ['confirm', 'cancel'],
  setup(props, { emit }) {
    const selectedFolderId = ref<number | null>(null);
    const isLoading = ref(false);
    
    // Check if root folder should be disabled
    const isRootDisabled = computed(() => {
      // If all notes are already at root, disable the root option
      return props.currentFolderId === null && props.notes.every(note => note.folder_id === null);
    });
    
    // Check if a folder should be disabled
    const isDisabled = (folder: Folder) => {
      // If all notes are already in this folder, disable it
      return props.notes.every(note => note.folder_id === folder.id);
    };
    
    // Check if Save button should be enabled
    const canConfirm = computed(() => {
      // If no destination is selected yet
      if (selectedFolderId.value === undefined) {
        return false;
      }
      
      // If current folder is selected and all notes are already there
      if (selectedFolderId.value === props.currentFolderId && 
          props.notes.every(note => note.folder_id === props.currentFolderId)) {
        return false;
      }
      
      return true;
    });
    
    // Error message for invalid selections
    const errorMessage = computed(() => {
      // If no destination is selected yet
      if (selectedFolderId.value === undefined) {
        return "Select a destination folder";
      }
      
      // If current folder is selected and all notes are already there
      if (selectedFolderId.value === props.currentFolderId && 
          props.notes.every(note => note.folder_id === props.currentFolderId)) {
        return "Notes are already in this folder";
      }
      
      return "";
    });
    
    // Get name of the selected folder for display
    const getSelectedFolderName = computed(() => {
      if (selectedFolderId.value === null) {
        return "All Folders (Root)";
      }
      
      const folder = props.folders.find(f => f.id === selectedFolderId.value);
      return folder ? folder.name : "";
    });
    
    const selectFolder = (folderId: number | null) => {
      selectedFolderId.value = folderId;
    };
    
    const confirm = () => {
      isLoading.value = true;
      emit('confirm', selectedFolderId.value);
      
      // Safeguard to reset loading state
      setTimeout(() => {
        isLoading.value = false;
      }, 5000);
    };
    
    const cancel = () => {
      emit('cancel');
    };
    
    return {
      selectedFolderId,
      isLoading,
      isDisabled,
      isRootDisabled,
      canConfirm,
      errorMessage,
      getSelectedFolderName,
      selectFolder,
      confirm,
      cancel
    };
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background-color: var(--color-modal-bg);
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--color-card-hover-shadow);
  width: 450px;
  max-width: 90%;
  font-family: 'Montserrat', sans-serif;
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.save-info {
  margin-bottom: 20px;
}

.save-info p {
  margin: 0 0 10px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.selected-items-summary {
  background-color: var(--color-bg-secondary);
  padding: 10px;
  border-radius: 6px;
  margin-top: 8px;
}

.selected-items-summary p {
  margin: 0;
  font-weight: 500;
  color: var(--color-text-primary);
}

.folder-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  margin-bottom: 16px;
}

.folder-list {
  padding: 8px 0;
}

.folder-item {
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.folder-item:hover:not(.disabled) {
  background-color: var(--color-nav-item-hover);
}

.folder-item.selected {
  background-color: var(--color-nav-item-active);
}

.folder-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-bg-tertiary);
  position: relative;
}

.folder-item.disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  pointer-events: none;
}

.folder-indent {
  display: flex;
  align-items: center;
  width: 100%;
}

.folder-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.disabled-text {
  color: var(--color-text-muted);
}

.disabled-reason {
  font-size: 11px;
  color: var(--color-error);
  margin-left: 8px;
  font-style: italic;
}

.no-folders {
  padding: 15px;
  text-align: center;
  color: var(--color-text-secondary);
}

.selected-destination {
  background-color: var(--color-nav-item-active);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 16px;
}

.destination-info {
  display: flex;
  align-items: center;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-bottom: 4px;
  text-align: right;
}

.loading-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-btn-primary-text);
  animation: spin 1s ease-in-out infinite;
  margin-right: 6px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
}

.btn-primary:disabled {
  background-color: var(--color-bg-tertiary);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-btn-secondary-hover);
}

.btn-secondary:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
</style>
